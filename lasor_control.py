'''
实验名称：UART（串口通信）带激光控制 - 黑色方框识别版
作者：01Studio
实验平台：01Studio CanMV K230
说明：通过摄像头识别黑色方框并控制激光笔
'''

# 导入模块
from machine import UART, FPIOA, Pin
import time
from media.sensor import *
from media.display import *

# 将引脚 2 配置为输出模式，无上下拉，驱动能力为 7
laser = Pin(14, Pin.OUT, pull=Pin.PULL_NONE, drive=7)


# 硬件初始化
fpioa = FPIOA()
sensor_id = 2
top_id = 1
button_id = 0

# UART初始化
fpioa.set_function(11, FPIOA.UART2_TXD)
fpioa.set_function(12, FPIOA.UART2_RXD)
uart = UART(UART.UART2, 115200)

# 激光笔初始化
#fpioa.set_function(14, FPIOA.GPIO14)
#laser = Pin(14, Pin.OUT)
#laser = PWM(Pin(14), freq=1000, duty=50)  # 50%占空比
laser.value(5)

def test_pin(pin_num):
    try:
        pin = Pin(pin_num, Pin.OUT)
        pin.value(1)
        print(f"Pin{pin_num} 高电平输出测试成功")
        pin.value(0)
        return True
    except Exception as e:
        print(f"Pin{pin_num} 测试失败: {e}")
        return False

if test_pin(14):
    print("GPIO14 可用")
else:
    print("请尝试其他GPIO引脚")


class PIDController:
    def __init__(self, kp=1.0, ki=0.0, kd=0.0, dt=1.0, integral_limit=None):
        self.kp = kp
        self.ki = ki / dt
        self.kd = kd / dt
        self.dt = dt
        self.prev_error = 0
        self.integral = 0
        self.integral_limit = integral_limit

    def update(self, setpoint, measured_value):
        error = setpoint - measured_value
        self.integral += error * self.dt
        if self.integral_limit is not None:
            self.integral = max(min(self.integral, self.integral_limit), -self.integral_limit)
        derivative = (error - self.prev_error) / self.dt
        self.prev_error = error
        return self.kp * error + self.ki * self.integral + self.kd * derivative

def set_servo_angle(servo_id, target_angle, duration_ms=300):
    """发送舵机角度控制指令"""
    angle_value = int(target_angle * 10)
    data = bytearray([
        0x12, 0x4C, 0x0D, 0x0B, servo_id,
        angle_value & 0xFF, (angle_value >> 8) & 0xFF,
        (angle_value >> 16) & 0xFF, (angle_value >> 24) & 0xFF,
        duration_ms & 0xFF, (duration_ms >> 8) & 0xFF,
        (duration_ms >> 16) & 0xFF, (duration_ms >> 24) & 0xFF,
        0x00, 0x00
    ])
    data.append(sum(data) & 0xFF)
    uart.write(data)
    print(f"Set servo {servo_id} to {target_angle}°")

# 初始化舵机位置
set_servo_angle(top_id, 0)
set_servo_angle(button_id, 0)
time.sleep(1)

try:
    # 摄像头初始化
    sensor = Sensor(id=sensor_id)
    sensor.reset()
    sensor.set_framesize(width=800, height=480)
    sensor.set_pixformat(Sensor.RGB565)
    Display.init(Display.ST7701, to_ide=True)
    MediaManager.init()
    sensor.run()

    # 黑色方框检测参数
    black_threshold = [(0, 50, -20, 20, -20, 20)]  # LAB黑色阈值
    min_rect_area = 2000  # 最小矩形面积
    aspect_ratio_range = (0.7, 1.5)  # 宽高比范围

#    # PID控制器
#    pid_y = PIDController(kp=0.3, ki=0.1, kd=0.004)
#    pid_x = PIDController(kp=0.3, ki=0.1, kd=0.004)

    pid_y = PIDController(kp=0.1, ki=0, kd=0)
    pid_x = PIDController(kp=0.1, ki=0, kd=0)

#    # 强制限制输出角度（关键修复）
#    out_x = max(-90, min(90, -pid_x.update(target_x, rect_center[0])))
#    out_y = max(-90, min(90, pid_y.update(target_y, rect_center[1])))

    TARGET_X = 645  # 图像目标中心X
    TARGET_Y = 344  # 图像目标中心Y
    MAX_SERVO_ANGLE = 90  # 舵机最大角度限制

    # 在程序开始时打印配置
    print(f"PID配置: kp={pid_x.kp}, ki={pid_x.ki}, kd={pid_x.kd}")
    print(f"目标位置: ({TARGET_X}, {TARGET_Y})")
    print(f"舵机限制: ±{MAX_SERVO_ANGLE}°")

    while True:
        os.exitpoint()
        img = sensor.snapshot(chn=CAM_CHN_ID_0)

        # 1. 检测黑色区域
        black_blobs = img.find_blobs(black_threshold, area_threshold=1000, merge=True)
        rect_center = None

        if black_blobs:
            for blob in black_blobs:
                # 2. 在黑色区域内寻找矩形
                rects = img.find_rects(threshold=3000, roi=(blob.x(), blob.y(), blob.w(), blob.h()))

                for rect in rects:
                    # 3. 筛选符合条件的矩形
                    w, h = rect.w(), rect.h()
                    area = w * h
                    aspect_ratio = w / h if h != 0 else 0

                    if (area >= min_rect_area and
                        aspect_ratio_range[0] <= aspect_ratio <= aspect_ratio_range[1]):

                        # 计算矩形中心
                        rect_center_x = rect.x() + w // 2
                        rect_center_y = rect.y() + h // 2
                        rect_center = (rect_center_x, rect_center_y)
                        img.draw_rectangle(rect.rect(), color=(255, 0, 0))
                        img.draw_cross(rect_center[0], rect_center[1], color=(0, 255, 0), size=10)

                        # 调整为更大的范围（例如：中心±30像素）
                        x_center, y_center = 645, 344  # 理论中心点
                        range_offset = 30  # 允许的偏差范围

                        if (x_center - range_offset <= rect_center[0] <= x_center + range_offset) and \
                           (y_center - range_offset <= rect_center[1] <= y_center + range_offset):
#                            laser.value(1)
                            print("激光开启 - 目标居中")

#                        # 激光控制（中心区域200-220x90-110）
#                        if (200 <= rect_center[0] <= 220) and (90 <= rect_center[1] <= 110):
#                            laser.value(1)
#                            print("激光开启 - 目标居中")
#                        else:
#                            laser.value(0)

#                        # PID控制
                        target_x = 645  # 目标X中心
                        target_y = 344  # 目标Y中心
#                        out_x = -pid_x.update(target_x, rect_center[0])
#                        out_y = pid_y.update(target_y, rect_center[1]
# PID控制（使用预定义的TARGET_X/Y）
                        out_x = max(-MAX_SERVO_ANGLE, min(MAX_SERVO_ANGLE,
                                -pid_x.update(TARGET_X, rect_center[0])))
                        out_y = max(-MAX_SERVO_ANGLE, min(MAX_SERVO_ANGLE,
                                pid_y.update(TARGET_Y, rect_center[1])))

                        print(f"目标位置: ({TARGET_X}, {TARGET_Y})")
                        print(f"实际位置: {rect_center}")
                        print(f"PID输出: X={out_x:.1f}°, Y={out_y:.1f}°")


                        set_servo_angle(button_id, out_x)
                        set_servo_angle(top_id, out_y)
                        print(f"PID输出: X={out_x:.1f}, Y={out_y:.1f}")
                        break  # 只处理第一个有效矩形

#        # 未检测到目标时关闭激光
#        if not rect_center:
#            laser.value(0)

        Display.show_image(img)

except KeyboardInterrupt as e:
    print("程序终止:", e)
except BaseException as e:
    print(f"异常: {e}")
finally:
    # 清理资源
    if isinstance(sensor, Sensor):
        sensor.stop()
    Display.deinit()
#    laser.value(0)  # 确保激光关闭
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    MediaManager.deinit()
